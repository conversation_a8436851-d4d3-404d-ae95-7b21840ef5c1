import type { SVGProps } from 'react';

export function PlusOneIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      {/* Plus symbol */}
      <path d="M8 8v8" />
      <path d="M4 12h8" />
      {/* Number "1" */}
      <path d="M16 8v8" />
      <path d="M14 10l2-2" />
      <path d="M14 16h4" />
    </svg>
  );
}
