'use client';

import { Settings, Languages, Maximize, Minimize } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { SettingsSheet } from './settings-sheet';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useLanguage } from '@/providers/language-provider';
import { UsaFlag } from './icons/usa-flag';
import { SpainFlag } from './icons/spain-flag';
import { APP_NAME } from '@/lib/constants';
import { useFullscreen } from '@/providers/fullscreen-provider';

export function AppHeader() {
  const { language, setLanguage } = useLanguage();
  const { isFullscreen, toggleFullscreen } = useFullscreen();

  return (
    <header className="p-4 flex justify-between items-center">
      <h1 className="text-xl md:text-2xl font-bold text-foreground font-headline">{APP_NAME}</h1>
      <div className="flex items-center gap-2">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="icon">
              <Languages className="h-5 w-5" />
              <span className="sr-only">Change language</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onSelect={() => setLanguage('en')}>
              <UsaFlag className="w-5 h-5 mr-2" />
              English
            </DropdownMenuItem>
            <DropdownMenuItem onSelect={() => setLanguage('es')}>
              <SpainFlag className="w-5 h-5 mr-2" />
              Español
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        <SettingsSheet>
          <Button variant="outline" size="icon">
            <Settings className="h-5 w-5" />
            <span className="sr-only">Settings</span>
          </Button>
        </SettingsSheet>

        <Button variant="outline" size="icon" onClick={toggleFullscreen}>
            {isFullscreen ? <Minimize className="h-5 w-5" /> : <Maximize className="h-5 w-5" />}
            <span className="sr-only">{isFullscreen ? 'Exit Fullscreen' : 'Enter Fullscreen'}</span>
        </Button>
      </div>
    </header>
  );
}
