'use client';

import { useTimer } from '@/providers/timer-provider';
import { useLanguage } from '@/providers/language-provider';
import { Button } from './ui/button';
import { Play, Pause, SkipForward } from 'lucide-react';
import { cn } from '@/lib/utils';
import { PlusOneIcon } from './icons/plus-one';

export function TimerControls() {
  const { isActive, startTimer, pauseTimer, skipTimer, addMinute } = useTimer();
  const { t } = useLanguage();

  const buttonClasses = "rounded-full w-20 h-20 md:w-24 md:h-24 text-base md:text-lg";
  const iconClasses = "w-8 h-8 md:w-10 md:h-10";
  const smallIconClasses = "w-6 h-6 md:w-8 md:h-8";
  const smallButtonClasses = "w-14 h-14 md:w-16 md:h-16 rounded-full";
  const plusOneButtonClasses = "w-21 h-21 md:w-24 md:h-24 rounded-full";
  const plusOneIconClasses = "w-9 h-9 md:w-12 md:h-12";


  return (
    <div className="flex items-center justify-center gap-2 landscape:gap-4">
      {isActive ? (
        <Button onClick={pauseTimer} size="lg" className={cn(buttonClasses)}>
          <Pause className={cn(iconClasses)} />
          <span className="sr-only">{t('controls.pause')}</span>
        </Button>
      ) : (
        <Button onClick={startTimer} size="lg" className={cn(buttonClasses)}>
          <Play className={cn(iconClasses)} />
          <span className="sr-only">{t('controls.start')}</span>
        </Button>
      )}

      <Button onClick={skipTimer} variant="outline" size="icon" className={cn(smallButtonClasses)}>
        <SkipForward className={cn(smallIconClasses)} />
        <span className="sr-only">{t('controls.skip')}</span>
      </Button>
      
      <Button onClick={addMinute} variant="outline" size="icon" className={cn(smallButtonClasses)}>
        <PlusOneIcon className={cn(smallIconClasses)} />
        <span className="sr-only">{t('controls.add_minute')}</span>
      </Button>
    </div>
  );
}
