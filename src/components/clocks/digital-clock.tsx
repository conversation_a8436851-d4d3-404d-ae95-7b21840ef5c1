import { cn } from '@/lib/utils';

interface DigitalClockProps {
  time: number;
  className?: string;
}

export function DigitalClock({ time, className }: DigitalClockProps) {
  const minutes = Math.floor(time / 60);
  const seconds = time % 60;

  return (
    <div className={cn("text-6xl md:text-8xl lg:text-9xl font-bold font-mono text-foreground tabular-nums landscape:text-9xl landscape:lg:text-[12rem]", className)}>
      <span>{String(minutes).padStart(2, '0')}</span>
      <span>:</span>
      <span>{String(seconds).padStart(2, '0')}</span>
    </div>
  );
}
