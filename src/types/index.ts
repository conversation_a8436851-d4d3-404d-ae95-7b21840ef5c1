export type ClockStyle = 'digital' | 'analog' | 'vintage';
export type Theme = 'light' | 'dark' | 'system';
export type Language = 'en' | 'es';
export type TimerMode = 'focus' | 'shortBreak' | 'longBreak';
export type ColorPalette = 'default' | 'forest' | 'ocean' | 'sunset' | 'rose';

export interface Settings {
  focusDuration: number;
  shortBreakDuration: number;
  longBreakDuration: number;
  sessionsBeforeLongBreak: number;
  clockStyle: ClockStyle;
  backgrounds: {
    focus: { url: string; hint: string };
    shortBreak: { url:string; hint: string };
    longBreak: { url: string; hint: string };
  };
  sounds: {
    focusEnd: string;
    shortBreakEnd: string;
    longBreakEnd: string;
  };
  notifications: {
    enabled: boolean;
  };
  theme: Theme;
  language: Language;
  colorPalette: ColorPalette;
}

export interface SessionRecord {
  id: string;
  type: TimerMode;
  duration: number; // in minutes
  completedAt: string; // ISO string
}
